// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:tts_truong_lai/value_notifier_list.dart';

class TTSTruongLaiBody extends StatefulWidget {
  const TTSTruongLaiBody({super.key});

  @override
  State<TTSTruongLaiBody> createState() => _TTSTruongLaiBodyState();
}

class _TTSTruongLaiBodyState extends State<TTSTruongLaiBody> {
  late TextEditingController controller;
  @override
  void initState() {
    _scrollController = ScrollController();
    controller = TextEditingController(text: '');
    super.initState();
  }

  ValueNotifierList<_Persion> students = ValueNotifierList([]);
  ValueNotifierList<_Persion> studentsAfter = ValueNotifierList([]);
  ValueNotifier<_Persion?> selectedStudents = ValueNotifier(null);
  int indexSelect = 0;
  late ScrollController _scrollController;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SizedBox(width: 150, child: _buildInputNumber()),
                ElevatedButton(
                  onPressed: () {
                    final total = int.tryParse(controller.text) ?? 0;
                    showModal(
                      context: context,
                      builder: (_) {
                        return AlertDialog(
                          actions: [
                            ElevatedButton(
                              onPressed: () {},
                              child: Text('Huyr'),
                            ),
                            ElevatedButton(
                              onPressed: () {
                                for (var i = 1; i <= total; i++) {
                                  final sbd = i.toString().padLeft(
                                    total.toString().length,
                                    '0',
                                  );

                                  StringBuffer buffer = StringBuffer();
                                  for (int i = 0; i < sbd.length; i++) {
                                    if (i > 0) buffer.write(' ');
                                    buffer.write(sbd[i]);
                                  }
                                  String result = buffer.toString();
                                  final persion = _Persion(sbd, 'xin mời thí sinh $result chuẩn bị thi',false, false);
                                  students.add(persion);
                                }
                              },
                              child: Text('Xac nhan'),
                            ),
                          ],
                          title: Text('Xac nhan so luong'),
                          insetPadding: EdgeInsets.zero,
                          contentPadding: EdgeInsets.all(12),
                          content: SizedBox(
                            width: 256,
                            height: 156,
                            child: Text.rich(
                              TextSpan(
                                text: 'so luong thi sinh du thi la : ',
                                children: [
                                  TextSpan(
                                    text: total.toString(),
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                  child: Text('Xac nhan'),
                ),
              ],
            ),
            Row(
              children: [
                SizedBox(
                  width: 300,
                  height: MediaQuery.sizeOf(context).height * .6,
                  child: Column(
                    children: [
                      Expanded(
                        child: ValueListenableBuilder(
                          valueListenable: students,
                          builder: (_, vStudent, child) {
                            return ValueListenableBuilder(
                              valueListenable: selectedStudents,
                              builder: (context, vSelectedStudent, child) {
                                return ListView.builder(
                                  controller: _scrollController,
                                  itemCount: vStudent.length,
                                  itemBuilder: (_, i) {
                                    final item = vStudent[i];
                                    return ListTile(
                                      key: GlobalObjectKey(item),
                                      tileColor: vSelectedStudent?.id == item.id
                                      ? Colors.blueGrey: Colors.white,
                                      selectedColor: Colors.amber,
                                      selected: vSelectedStudent?.id == item.id,
                                      title: Text('${item.id} - ${item.text}'),
                                      trailing: TextButton(
                                        onPressed: vSelectedStudent?.id == item.id ? null : () {
                                          selectedStudents.value = item;
                                        },
                                        child: Text('chon'),
                                      ),
                                    );
                                  },
                                );
                              }
                            );
                          },
                        ),
                      ),
                      Container(color: Colors.amber, width: double.infinity, height: 60,
                      child: Center(
                        child: ValueListenableBuilder(
                          valueListenable: selectedStudents,
                          builder: (_, vStudent, child) {
                   
                              return vStudent == null
                              ? ElevatedButton(onPressed: (){
                                final off = GlobalObjectKey(students.getItem(0))
                                 .currentContext;
                                    if (off != null) {
                                            Scrollable.ensureVisible(off, duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
                                            // _scrollController.animateTo(
                                            //   off.localToGlobal(Offset.zero).dy,
                                            //   duration: Duration(milliseconds: 500),
                                            //   curve: Curves.easeInOut,
                                            // );
                                          }
                                selectedStudents.value = students.getItem(0);
                              
                            }, child: Text('Bat dau thi')):
                             ElevatedButton(onPressed: (){
                              indexSelect++;
                              selectedStudents.value = students.getItem(indexSelect);
                                 final off = GlobalObjectKey(students.getItem(indexSelect))
                                 .currentContext;
                                          if (off != null) {
                                            Scrollable.ensureVisible(off, duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
                                            // _scrollController.animateTo(
                                            //   off.localToGlobal(Offset.zero).dy,
                                            //   duration: Duration(milliseconds: 500),
                                            //   curve: Curves.easeInOut,
                                            // );
                                          }
                             },
                              child: Text('thi sinh tiep theo'));
                          },
                        ),
                      ),
                      ),
                    ],
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: selectedStudents,
                  builder: (_, vStudent, child) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 200,
                          height: 60,
                          child: Card(child: Center(child: Text(vStudent?.text ?? '')))),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () {
                              studentsAfter.add(vStudent?? _Persion('', '', false,false));
                              final idxStudent = students.value.indexWhere((final e)
                              => e.id == vStudent?.id);
                              if(idxStudent != -1){
                                students.updateValuebyIndex(idxStudent, vStudent?.copyWith(isVang: true) ?? _Persion('', '', false,false));
                              }
                            

                            },
                            child: Text("Bỏ qua"),
                          ),
                          ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                            
                            ),
                            onPressed: () {},
                            child: Text('Thi sinh có mặt'),
                          ),
                        ],)
                      ],
                    );
                  },
                ),
                   SizedBox(
                  width: 300,
                  height: MediaQuery.sizeOf(context).height * .6,
                  child: Column(
                    children: [
                      Expanded(
                        child: ValueListenableBuilder(
                          valueListenable: studentsAfter,
                          builder: (_, vStudent, child) {
                            return ValueListenableBuilder(
                              valueListenable: selectedStudents,
                              builder: (context, vSelectedStudent, child) {
                                return ListView.builder(
                                  controller: _scrollController,
                                  itemCount: vStudent.length,
                                  itemBuilder: (_, i) {
                                    final item = vStudent[i];
                                    return ListTile(
                                      key: GlobalObjectKey(item),
                                      tileColor: vSelectedStudent?.id == item.id
                                      ? Colors.blueGrey: Colors.white,
                                      selectedColor: Colors.amber,
                                      selected: vSelectedStudent?.id == item.id,
                                      title: Text('${item.id} - ${item.text}'),
                                      trailing: TextButton(
                                        onPressed: vSelectedStudent?.id == item.id ? null : () {
                                          selectedStudents.value = item;
                                        },
                                        child: Text('chon'),
                                      ),
                                    );
                                  },
                                );
                              }
                            );
                          },
                        ),
                      ),
                      Container(color: Colors.amber, width: double.infinity, height: 60,
                      child: Center(
                        child: ValueListenableBuilder(
                          valueListenable: selectedStudents,
                          builder: (_, vStudent, child) {
                   
                              return vStudent == null
                              ? ElevatedButton(onPressed: (){
                                final off = GlobalObjectKey(students.getItem(0))
                                 .currentContext;
                                    if (off != null) {
                                            Scrollable.ensureVisible(off, duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
                                            // _scrollController.animateTo(
                                            //   off.localToGlobal(Offset.zero).dy,
                                            //   duration: Duration(milliseconds: 500),
                                            //   curve: Curves.easeInOut,
                                            // );
                                          }
                                selectedStudents.value = students.getItem(0);
                              
                            }, child: Text('Bat dau thi')):
                             ElevatedButton(onPressed: (){
                              indexSelect++;
                              selectedStudents.value = students.getItem(indexSelect);
                                 final off = GlobalObjectKey(students.getItem(indexSelect))
                                 .currentContext;
                                          if (off != null) {
                                            Scrollable.ensureVisible(off, duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
                                            // _scrollController.animateTo(
                                            //   off.localToGlobal(Offset.zero).dy,
                                            //   duration: Duration(milliseconds: 500),
                                            //   curve: Curves.easeInOut,
                                            // );
                                          }
                             },
                              child: Text('thi sinh tiep theo'));
                          },
                        ),
                      ),
                      ),
                    ],
                  ),
                ),
              
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputNumber() {
    return TextField(
      controller: controller,
      keyboardType: TextInputType.numberWithOptions(signed: true),
      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      decoration: InputDecoration(
        border: OutlineInputBorder(),
        isDense: true,
        contentPadding: EdgeInsets.all(12),
      ),
    );
  }
}

class _Persion {
  String id;
  String text;
  bool isDone = false;
  bool isVang = false;
  _Persion(this.id, this.text, this.isDone, this.isVang);

  _Persion copyWith({
    String? id,
    String? text,
    bool? isDone,
    bool? isVang,
  }) {
    return _Persion(
      id ?? this.id,
      text ?? this.text,
      isDone ?? this.isDone,
      isVang ?? this.isVang,
    );
  }
}
